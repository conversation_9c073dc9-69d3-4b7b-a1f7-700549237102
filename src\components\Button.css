.button {
  display: flex;
  gap: 10px;
  justify-content: center;
  align-items: center;
  padding: 20px 32px;
  border: 2px solid transparent;
  cursor: pointer;
  font-family: 'Merriweather', serif;
  font-weight: bold;
  letter-spacing: 0.05em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

/* Smooth background transition effect */
.button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(146, 125, 112, 0.3);
}

.button:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(146, 125, 112, 0.2);
}

/* Variants */
.button--primary {
  background-color: #927D70;
  color: white;
  border-color: #927D70;
}

.button--primary::before {
  background-color: #927D70;
}

.button--primary:hover {
  background-color: transparent;
  color: #927D70;
  border-color: #927D70;
}

.button--primary:hover::before {
  background-color: transparent;
}

.button--secondary {
  background-color: transparent;
  color: #966F33;
  border-color: #927D70;
}

.button--secondary:hover {
  background-color: #927D70;
  color: white;
  border-color: #927D70;
}

/* Disabled state */
.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.button:disabled:hover {
  background-color: #927D70;
  color: white;
  border-color: #927D70;
  transform: none;
  box-shadow: none;
}

.button--secondary:disabled:hover {
  background-color: transparent;
  color: #966F33;
  border-color: #927D70;
}

/* Sizes */
.button--small {
  padding: 12px 24px;
  font-size: 14px;
  line-height: 16px;
}

.button--medium {
  padding: 20px 32px;
  font-size: 16px;
  line-height: 16px;
  height: 59px;
  min-width: 156px;
}

.button--large {
  padding: 20px 32px;
  font-size: 20px;
  line-height: 24px;
  letter-spacing: 0.1em;
  height: 65px;
  min-width: 206px;
}

/* FOMO Dialog Styles */
.button-container {
  position: relative;
  display: inline-block;
}

.fomo-dialog {
  position: absolute;
  top: -80px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;
  pointer-events: auto;
  will-change: transform, opacity;
}

.fomo-dialog__content {
  background: linear-gradient(135deg, #DC2626 0%, #EF4444 100%);
  border: 2px solid #B91C1C;
  padding: 12px 20px;
  min-width: 220px;
  max-width: 280px;
  box-shadow:
    0 8px 32px rgba(220, 38, 38, 0.4),
    0 4px 16px rgba(0, 0, 0, 0.2);
  position: relative;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Arrow pointing down to button */
.fomo-dialog__content::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #DC2626;
}

.fomo-dialog__title {
  font-family: 'Baskerville Old Face', 'Times New Roman', serif;
  font-size: 16px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 4px 0;
  text-align: center;
  letter-spacing: 0.02em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.fomo-dialog__message {
  font-family: 'Merriweather', serif;
  font-size: 12px;
  font-weight: 400;
  color: #FEF2F2;
  margin: 0;
  text-align: center;
  letter-spacing: 0.05em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  line-height: 1.3;
}

/* Hover effects for enhanced urgency */
.fomo-dialog:hover .fomo-dialog__content {
  background: linear-gradient(135deg, #B91C1C 0%, #DC2626 100%);
  box-shadow:
    0 12px 40px rgba(220, 38, 38, 0.5),
    0 6px 20px rgba(0, 0, 0, 0.25);
  transform: translateY(-2px);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.fomo-dialog:hover .fomo-dialog__title {
  color: #FFFFFF;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
}

/* Responsive */
@media (max-width: 640px) {
  .button--medium,
  .button--large {
    width: 100%;
    padding-top: 10px;
    padding-bottom: 10px;
    height: 45px;

  }

  .fomo-dialog {
    top: -70px;
    left: 50%;
    transform: translateX(-50%);
  }

  .fomo-dialog__content {
    min-width: 200px;
    max-width: 250px;
    padding: 10px 16px;
  }

  .fomo-dialog__title {
    font-size: 14px;
  }

  .fomo-dialog__message {
    font-size: 11px;
  }
}
